#!/usr/bin/env node

/**
 * Test script to verify the project filtering bug fix
 * 
 * This test verifies that when the same user uploads requirements to different projects,
 * the GET /api/v1/requirements endpoint correctly filters by project_id.
 * 
 * Bug: Previously, user lookup was only by userId, causing requirements to be
 * associated with the wrong project when the same user worked on multiple projects.
 * 
 * Fix: User lookup now uses userId + companyId + projectId combination.
 */

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

// Configuration
const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';
const JWT_TOKEN = process.env.JWT_TOKEN || 'your-jwt-token-here';

// Test data
const TEST_USER_ID = 'test-user-12345';
const TEST_COMPANY_ID = 'test-company-67890';
const PROJECT_1_ID = 'project-1-uuid';
const PROJECT_2_ID = 'project-2-uuid';

async function createTestFile(content, filename) {
  fs.writeFileSync(filename, content);
  console.log(`✅ Created test file: ${filename}`);
}

async function uploadRequirement(projectId, requirementName, filename) {
  try {
    const formData = new FormData();
    formData.append('file', fs.createReadStream(filename));
    formData.append('name', requirementName);
    formData.append('user_id', TEST_USER_ID);
    formData.append('company_id', TEST_COMPANY_ID);
    formData.append('project_id', projectId);

    const response = await axios.post(
      `${BASE_URL}/api/v1/requirements/upload/async`,
      formData,
      {
        headers: {
          'Authorization': `Bearer ${JWT_TOKEN}`,
          ...formData.getHeaders()
        }
      }
    );

    console.log(`✅ Uploaded requirement "${requirementName}" to project ${projectId}`);
    console.log(`   Job ID: ${response.data.jobId}`);
    return response.data.jobId;
  } catch (error) {
    console.error(`❌ Failed to upload requirement to project ${projectId}:`, error.response?.data || error.message);
    throw error;
  }
}

async function waitForJobCompletion(jobId, maxWaitTime = 30000) {
  const startTime = Date.now();
  
  while (Date.now() - startTime < maxWaitTime) {
    try {
      const response = await axios.get(
        `${BASE_URL}/api/v1/requirements/jobs/${jobId}/status`,
        {
          headers: { 'Authorization': `Bearer ${JWT_TOKEN}` }
        }
      );

      const status = response.data.status;
      console.log(`   Job ${jobId} status: ${status} (${response.data.progress.percentage}%)`);

      if (status === 'completed') {
        console.log(`✅ Job ${jobId} completed successfully`);
        return true;
      } else if (status === 'failed') {
        console.error(`❌ Job ${jobId} failed:`, response.data.error);
        return false;
      }

      // Wait 2 seconds before checking again
      await new Promise(resolve => setTimeout(resolve, 2000));
    } catch (error) {
      console.error(`❌ Error checking job status:`, error.response?.data || error.message);
      return false;
    }
  }

  console.error(`❌ Job ${jobId} did not complete within ${maxWaitTime}ms`);
  return false;
}

async function getRequirementsByProject(projectId) {
  try {
    const response = await axios.get(
      `${BASE_URL}/api/v1/requirements?project_id=${projectId}&limit=50`,
      {
        headers: { 'Authorization': `Bearer ${JWT_TOKEN}` }
      }
    );

    console.log(`✅ Retrieved ${response.data.data.length} requirements for project ${projectId}`);
    return response.data.data;
  } catch (error) {
    console.error(`❌ Failed to get requirements for project ${projectId}:`, error.response?.data || error.message);
    throw error;
  }
}

async function runTest() {
  console.log('🧪 Testing Project Filtering Bug Fix\n');
  console.log('This test verifies that requirements are correctly filtered by project_id');
  console.log('when the same user uploads to different projects.\n');

  try {
    // Step 1: Create test files
    console.log('📝 Step 1: Creating test files...');
    await createTestFile(
      'Project 1 Requirement\n\nThis requirement belongs to Project 1.\n\nFeatures:\n- Feature A\n- Feature B',
      'project1-requirement.txt'
    );
    await createTestFile(
      'Project 2 Requirement\n\nThis requirement belongs to Project 2.\n\nFeatures:\n- Feature X\n- Feature Y',
      'project2-requirement.txt'
    );

    // Step 2: Upload requirements to different projects
    console.log('\n📤 Step 2: Uploading requirements to different projects...');
    const job1 = await uploadRequirement(PROJECT_1_ID, 'Project 1 Test Requirement', 'project1-requirement.txt');
    const job2 = await uploadRequirement(PROJECT_2_ID, 'Project 2 Test Requirement', 'project2-requirement.txt');

    // Step 3: Wait for uploads to complete
    console.log('\n⏳ Step 3: Waiting for uploads to complete...');
    const job1Success = await waitForJobCompletion(job1);
    const job2Success = await waitForJobCompletion(job2);

    if (!job1Success || !job2Success) {
      throw new Error('One or more upload jobs failed');
    }

    // Step 4: Test project filtering
    console.log('\n🔍 Step 4: Testing project filtering...');
    
    // Get requirements for Project 1
    const project1Requirements = await getRequirementsByProject(PROJECT_1_ID);
    
    // Get requirements for Project 2
    const project2Requirements = await getRequirementsByProject(PROJECT_2_ID);

    // Step 5: Verify results
    console.log('\n✅ Step 5: Verifying results...');
    
    // Find our test requirements
    const project1TestReq = project1Requirements.find(req => req.name === 'Project 1 Test Requirement');
    const project2TestReq = project2Requirements.find(req => req.name === 'Project 2 Test Requirement');

    // Verify Project 1 requirement is only in Project 1
    if (project1TestReq) {
      console.log('✅ Project 1 requirement found in Project 1 results');
    } else {
      console.error('❌ Project 1 requirement NOT found in Project 1 results');
    }

    // Verify Project 2 requirement is only in Project 2
    if (project2TestReq) {
      console.log('✅ Project 2 requirement found in Project 2 results');
    } else {
      console.error('❌ Project 2 requirement NOT found in Project 2 results');
    }

    // Verify no cross-contamination
    const project1HasProject2Req = project1Requirements.find(req => req.name === 'Project 2 Test Requirement');
    const project2HasProject1Req = project2Requirements.find(req => req.name === 'Project 1 Test Requirement');

    if (!project1HasProject2Req) {
      console.log('✅ Project 1 results do NOT contain Project 2 requirement (correct)');
    } else {
      console.error('❌ Project 1 results contain Project 2 requirement (BUG!)');
    }

    if (!project2HasProject1Req) {
      console.log('✅ Project 2 results do NOT contain Project 1 requirement (correct)');
    } else {
      console.error('❌ Project 2 results contain Project 1 requirement (BUG!)');
    }

    // Final result
    const testPassed = project1TestReq && project2TestReq && !project1HasProject2Req && !project2HasProject1Req;
    
    console.log('\n🎯 Test Results:');
    if (testPassed) {
      console.log('✅ ALL TESTS PASSED! Project filtering is working correctly.');
      console.log('   The bug has been successfully fixed.');
    } else {
      console.log('❌ TESTS FAILED! Project filtering is not working correctly.');
      console.log('   The bug may still exist or there are other issues.');
    }

  } catch (error) {
    console.error('\n❌ Test failed with error:', error.message);
  } finally {
    // Cleanup test files
    try {
      fs.unlinkSync('project1-requirement.txt');
      fs.unlinkSync('project2-requirement.txt');
      console.log('\n🧹 Cleaned up test files');
    } catch (e) {
      // Ignore cleanup errors
    }
  }
}

// Run the test
if (require.main === module) {
  runTest().catch(console.error);
}

module.exports = { runTest };
