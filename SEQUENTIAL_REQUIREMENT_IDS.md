# Sequential Requirement ID Generation Per Project

## 📋 Business Requirement

Requirement IDs should be **sequential per project**:
- Project A: R-001, R-002, R-003, R-004...
- Project B: R-001, R-002, R-003, R-004...
- Project C: R-001, R-002, R-003, R-004...

Each project maintains its own independent sequence starting from 1.

## 🔧 Implementation

### Previous Issues

1. **Original approach**: Simple global counter caused race conditions
2. **Timestamp approach**: Fixed race conditions but gave ugly IDs like `R-1981513266`

### Current Solution

Sequential numbering per project with race condition protection:

```typescript
// Generate sequential requirement ID per project with race condition protection
let requirementId: string;
let attempts = 0;
const maxAttempts = 10;

do {
  attempts++;
  
  // Count existing requirements for this specific project
  const projectRequirementsCount = await this.requirementRepository
    .createQueryBuilder('req')
    .innerJoin('req.uploadedBy', 'user')
    .where('user.projectId = :projectId', { projectId: uploadDto.project_id })
    .getCount();
  
  const nextNumber = projectRequirementsCount + 1;
  requirementId = `R-${String(nextNumber).padStart(3, '0')}`;
  
  // Check if this ID already exists for this project (double-check for race conditions)
  const existing = await this.requirementRepository
    .createQueryBuilder('req')
    .innerJoin('req.uploadedBy', 'user')
    .where('req.requirementId = :requirementId', { requirementId })
    .andWhere('user.projectId = :projectId', { projectId: uploadDto.project_id })
    .getOne();
  
  if (!existing) {
    break; // Found unique ID for this project
  }
  
  if (attempts >= maxAttempts) {
    throw new Error(`Failed to generate unique requirement ID for project ${uploadDto.project_id} after ${maxAttempts} attempts`);
  }
  
  // Wait a bit before retrying to reduce contention
  await new Promise(resolve => setTimeout(resolve, 50 + Math.random() * 50));
} while (attempts < maxAttempts);
```

## ✅ Features

1. **Sequential per project**: Each project starts from R-001
2. **Independent sequences**: Projects don't interfere with each other
3. **Race condition safe**: Handles concurrent uploads properly
4. **Retry logic**: Automatically retries if conflicts occur
5. **Clean format**: R-001, R-002, R-003 (3-digit padding)

## 📁 Files Modified

1. **`src/requirements/requirements.service.ts`** - Lines 91-127
2. **`src/requirements/processors/upload.processor.ts`** - Lines 74-113

## 🧪 Testing

Run the test to verify sequential numbering works correctly:

```bash
export BASE_URL="http://localhost:3000"
export JWT_TOKEN="your-jwt-token-here"
node test-sequential-requirement-ids.js
```

### What the Test Does

1. **Uploads 3 requirements to Project A** → Should get R-001, R-002, R-003
2. **Uploads 3 requirements to Project B** → Should get R-001, R-002, R-003
3. **Uploads 2 more to Project A** → Should get R-004, R-005
4. **Verifies** that each project has independent sequential numbering

### Expected Output

```
🧪 Testing Sequential Requirement ID Generation Per Project

📤 Step 1: Uploading 3 requirements to Project A...
✅ Uploaded "Project A Requirement 1" to project-a-sequential - Job ID: upload_xxx
✅ Uploaded "Project A Requirement 2" to project-a-sequential - Job ID: upload_yyy
✅ Uploaded "Project A Requirement 3" to project-a-sequential - Job ID: upload_zzz

📤 Step 2: Uploading 3 requirements to Project B...
✅ Uploaded "Project B Requirement 1" to project-b-sequential - Job ID: upload_aaa
✅ Uploaded "Project B Requirement 2" to project-b-sequential - Job ID: upload_bbb
✅ Uploaded "Project B Requirement 3" to project-b-sequential - Job ID: upload_ccc

📤 Step 3: Uploading 2 more requirements to Project A...
✅ Uploaded "Project A Requirement 4" to project-a-sequential - Job ID: upload_ddd
✅ Uploaded "Project A Requirement 5" to project-a-sequential - Job ID: upload_eee

⏳ Step 4: Waiting for all jobs to complete...
✅ 8 jobs completed successfully

🔍 Step 5: Verifying sequential requirement IDs...

📊 Results:
Project A: 5 requirements
Project B: 3 requirements

Project A IDs: R-001, R-002, R-003, R-004, R-005
Project B IDs: R-001, R-002, R-003

✅ Verification Results:
✅ Project A: Sequential numbering correct (R-001 to R-005)
✅ Project B: Sequential numbering correct (R-001 to R-003)
✅ Independent numbering: Both projects start from R-001 (correct)

🎯 Test Results:
✅ ALL TESTS PASSED! Sequential requirement ID generation is working correctly.
   - Project A has sequential IDs: R-001, R-002, R-003, R-004, R-005
   - Project B has sequential IDs: R-001, R-002, R-003
   - Each project has independent numbering sequences
```

## 🔍 How It Works

1. **Count existing requirements** for the specific project
2. **Generate next sequential number** (count + 1)
3. **Format as R-XXX** with 3-digit padding
4. **Double-check uniqueness** within the project
5. **Retry if conflict** (handles race conditions)
6. **Return clean sequential ID**

## 📈 Benefits

- ✅ **Clean, readable IDs**: R-001, R-002, R-003
- ✅ **Project isolation**: Each project has independent numbering
- ✅ **Race condition safe**: Handles concurrent uploads
- ✅ **Predictable**: Easy to understand and manage
- ✅ **Scalable**: Works with any number of projects

This approach gives you the clean, sequential requirement IDs you expect while maintaining system reliability and data integrity.
