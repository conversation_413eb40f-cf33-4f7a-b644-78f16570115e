# Critical Bug Fixes: Project Filtering & Race Condition

## 🐛 Bug #1: Project Filtering Issue

**Fatal Bug**: When user A uploads requirements to different projects, the requirements cannot be retrieved correctly when filtering by `project_id`.

### Root Cause

The user lookup logic in both `requirements.service.ts` and `upload.processor.ts` was only searching by `userId`, but the user table stores combinations of `userId + companyId + projectId`. This caused:

1. User A uploads requirement to Project 1 → Creates user record (userId: A, projectId: 1)
2. User A uploads requirement to Project 2 → Finds existing user record for Project 1 instead of creating new one
3. Requirement gets associated with wrong user record (Project 1 instead of Project 2)
4. When filtering by `project_id`, requirements don't show up in correct project

## 🔧 Fix Applied

Updated user lookup logic in both files to search by the complete combination:

### Before (Buggy Code)
```typescript
let user = await this.userRepository.findOne({
  where: { userId: uploadDto.user_id }  // ❌ Only searching by userId
});
```

### After (Fixed Code)
```typescript
let user = await this.userRepository.findOne({
  where: { 
    userId: uploadDto.user_id,
    companyId: uploadDto.company_id,
    projectId: uploadDto.project_id
  }
});
```

## 📁 Files Modified (Bug #1 Only)

1. **`src/requirements/requirements.service.ts`** - Lines 65-70 (user lookup fix)
2. **`src/requirements/processors/upload.processor.ts`** - Lines 48-53 (user lookup fix)

## 🧪 Testing the Fix

### Prerequisites

1. **Install dependencies** (if not already done):
   ```bash
   npm install axios form-data
   ```

2. **Set environment variables**:
   ```bash
   export BASE_URL="http://localhost:3000"
   export JWT_TOKEN="your-jwt-token-here"
   ```

3. **Ensure the server is running**:
   ```bash
   npm run start:dev
   ```

### Run the Test

```bash
node test-project-filtering-fix.js
```

### What the Test Does

1. **Creates two test files** with different content
2. **Uploads requirements** using the same user ID but different project IDs
3. **Waits for uploads** to complete (async processing)
4. **Retrieves requirements** filtered by each project ID
5. **Verifies** that:
   - Project 1 requirements only appear in Project 1 results
   - Project 2 requirements only appear in Project 2 results
   - No cross-contamination between projects

### Expected Output

```
🧪 Testing Project Filtering Bug Fix

📝 Step 1: Creating test files...
✅ Created test file: project1-requirement.txt
✅ Created test file: project2-requirement.txt

📤 Step 2: Uploading requirements to different projects...
✅ Uploaded requirement "Project 1 Test Requirement" to project project-1-uuid
✅ Uploaded requirement "Project 2 Test Requirement" to project project-2-uuid

⏳ Step 3: Waiting for uploads to complete...
✅ Job upload_xxx completed successfully
✅ Job upload_yyy completed successfully

🔍 Step 4: Testing project filtering...
✅ Retrieved X requirements for project project-1-uuid
✅ Retrieved Y requirements for project project-2-uuid

✅ Step 5: Verifying results...
✅ Project 1 requirement found in Project 1 results
✅ Project 2 requirement found in Project 2 results
✅ Project 1 results do NOT contain Project 2 requirement (correct)
✅ Project 2 results do NOT contain Project 1 requirement (correct)

🎯 Test Results:
✅ ALL TESTS PASSED! Project filtering is working correctly.
   The bug has been successfully fixed.
```

## 🔍 Manual Testing

You can also test manually using curl:

### 1. Upload to Project 1
```bash
curl -X POST "http://localhost:3000/api/v1/requirements/upload/async" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "file=@test1.txt" \
  -F "name=Project 1 Requirement" \
  -F "user_id=test-user-123" \
  -F "company_id=test-company-456" \
  -F "project_id=project-1-uuid"
```

### 2. Upload to Project 2 (Same User)
```bash
curl -X POST "http://localhost:3000/api/v1/requirements/upload/async" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "file=@test2.txt" \
  -F "name=Project 2 Requirement" \
  -F "user_id=test-user-123" \
  -F "company_id=test-company-456" \
  -F "project_id=project-2-uuid"
```

### 3. Filter by Project 1
```bash
curl -X GET "http://localhost:3000/api/v1/requirements?project_id=project-1-uuid" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 4. Filter by Project 2
```bash
curl -X GET "http://localhost:3000/api/v1/requirements?project_id=project-2-uuid" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## ✅ Verification

After the fix:
- ✅ Requirements uploaded to Project 1 only appear when filtering by Project 1
- ✅ Requirements uploaded to Project 2 only appear when filtering by Project 2
- ✅ Same user can work on multiple projects without cross-contamination
- ✅ Project filtering works correctly for all users

## 🚨 Impact

This was a **critical bug** that affected:
- Multi-project environments
- Users working across multiple projects
- Data integrity and project isolation
- API filtering functionality

The fix ensures proper project isolation and data integrity in multi-project scenarios.

## 🐛 Bug #2: Race Condition in Requirement ID Generation

**Critical Bug**: Concurrent uploads cause unique constraint violations due to duplicate requirement IDs.

### Root Cause

The requirement ID generation logic used a simple counter approach:

```typescript
const count = await this.requirementRepository.count();
const requirementId = `R-${String(count + 1).padStart(3, '0')}`;
```

When multiple uploads happen simultaneously:
1. Both read the same count (e.g., 5)
2. Both generate the same requirement ID (e.g., "R-006")
3. Both try to save, causing unique constraint violation: `duplicate key value violates unique constraint "UQ_bf3a4139dc77cd536e9954b7aaf"`

### Fix Applied

Replaced with timestamp + random approach with retry logic:

```typescript
// Generate unique requirement ID with retry logic to handle race conditions
let requirementId: string;
let attempts = 0;
const maxAttempts = 5;

do {
  attempts++;
  // Use timestamp + random for uniqueness
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  requirementId = `R-${timestamp}${random}`;

  // Check if this ID already exists
  const existing = await this.requirementRepository.findOne({
    where: { requirementId }
  });

  if (!existing) {
    break; // Found unique ID
  }

  if (attempts >= maxAttempts) {
    throw new Error(`Failed to generate unique requirement ID after ${maxAttempts} attempts`);
  }

  // Wait a bit before retrying
  await new Promise(resolve => setTimeout(resolve, 10));
} while (attempts < maxAttempts);
```

## 📁 All Files Modified (Updated)

### Bug #1 (Project Filtering):
1. **`src/requirements/requirements.service.ts`** - Lines 65-70
2. **`src/requirements/processors/upload.processor.ts`** - Lines 48-53

### Bug #2 (Race Condition):
3. **`src/requirements/requirements.service.ts`** - Lines 91-118
4. **`src/requirements/processors/upload.processor.ts`** - Lines 74-104

## 🧪 Testing Both Fixes (Updated)

### Test Project Filtering Fix
```bash
node test-project-filtering-fix.js
```

### Test Race Condition Fix
```bash
node test-concurrent-uploads.js
```

This test uploads 10 files concurrently and verifies:
- No duplicate requirement IDs are generated
- All uploads complete successfully
- No unique constraint violations occur

## ✅ Final Impact

These were **critical bugs** that affected:
- Multi-project environments (Bug #1)
- High-concurrency scenarios (Bug #2)
- Data integrity and system reliability
- API functionality and user experience

Both fixes ensure proper data isolation, prevent race conditions, and maintain system stability under concurrent load.
