#!/usr/bin/env node

/**
 * Test script to verify the race condition fix for requirement ID generation
 * 
 * This test uploads multiple files concurrently to ensure that:
 * 1. No duplicate requirement IDs are generated
 * 2. All uploads complete successfully
 * 3. The unique constraint violation is fixed
 */

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

// Configuration
const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';
const JWT_TOKEN = process.env.JWT_TOKEN || 'your-jwt-token-here';

// Test data
const TEST_USER_ID = 'test-user-concurrent';
const TEST_COMPANY_ID = 'test-company-concurrent';
const TEST_PROJECT_ID = 'test-project-concurrent';

async function createTestFile(content, filename) {
  fs.writeFileSync(filename, content);
  console.log(`✅ Created test file: ${filename}`);
}

async function uploadRequirement(index) {
  try {
    const filename = `concurrent-test-${index}.txt`;
    const content = `Concurrent Upload Test ${index}\n\nThis is test file number ${index} for testing concurrent uploads.\n\nFeatures:\n- Feature ${index}A\n- Feature ${index}B\n- Feature ${index}C`;
    
    // Create test file
    fs.writeFileSync(filename, content);

    const formData = new FormData();
    formData.append('file', fs.createReadStream(filename));
    formData.append('name', `Concurrent Test Requirement ${index}`);
    formData.append('user_id', TEST_USER_ID);
    formData.append('company_id', TEST_COMPANY_ID);
    formData.append('project_id', TEST_PROJECT_ID);

    const startTime = Date.now();
    const response = await axios.post(
      `${BASE_URL}/api/v1/requirements/upload/async`,
      formData,
      {
        headers: {
          'Authorization': `Bearer ${JWT_TOKEN}`,
          ...formData.getHeaders()
        }
      }
    );

    const uploadTime = Date.now() - startTime;
    console.log(`✅ Upload ${index} completed in ${uploadTime}ms - Job ID: ${response.data.jobId}`);
    
    // Cleanup test file
    fs.unlinkSync(filename);
    
    return {
      index,
      jobId: response.data.jobId,
      uploadTime,
      success: true
    };
  } catch (error) {
    console.error(`❌ Upload ${index} failed:`, error.response?.data || error.message);
    
    // Cleanup test file if it exists
    const filename = `concurrent-test-${index}.txt`;
    try {
      fs.unlinkSync(filename);
    } catch (e) {
      // Ignore cleanup errors
    }
    
    return {
      index,
      error: error.response?.data || error.message,
      success: false
    };
  }
}

async function waitForJobCompletion(jobId, maxWaitTime = 30000) {
  const startTime = Date.now();
  
  while (Date.now() - startTime < maxWaitTime) {
    try {
      const response = await axios.get(
        `${BASE_URL}/api/v1/requirements/jobs/${jobId}/status`,
        {
          headers: { 'Authorization': `Bearer ${JWT_TOKEN}` }
        }
      );

      const status = response.data.status;

      if (status === 'completed') {
        return { success: true, result: response.data.result };
      } else if (status === 'failed') {
        return { success: false, error: response.data.error };
      }

      // Wait 1 second before checking again
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  }

  return { success: false, error: 'Timeout waiting for job completion' };
}

async function runConcurrentTest() {
  console.log('🧪 Testing Concurrent Uploads (Race Condition Fix)\n');
  console.log('This test uploads multiple files simultaneously to verify that');
  console.log('the requirement ID generation race condition has been fixed.\n');

  const concurrentUploads = 10; // Number of concurrent uploads
  console.log(`📤 Starting ${concurrentUploads} concurrent uploads...\n`);

  try {
    // Start all uploads simultaneously
    const uploadPromises = [];
    for (let i = 1; i <= concurrentUploads; i++) {
      uploadPromises.push(uploadRequirement(i));
    }

    // Wait for all uploads to complete
    const uploadResults = await Promise.all(uploadPromises);

    // Analyze results
    const successful = uploadResults.filter(r => r.success);
    const failed = uploadResults.filter(r => !r.success);

    console.log(`\n📊 Upload Results:`);
    console.log(`✅ Successful: ${successful.length}/${concurrentUploads}`);
    console.log(`❌ Failed: ${failed.length}/${concurrentUploads}`);

    if (failed.length > 0) {
      console.log('\n❌ Failed uploads:');
      failed.forEach(f => {
        console.log(`   Upload ${f.index}: ${f.error}`);
      });
    }

    if (successful.length === 0) {
      console.log('\n❌ All uploads failed! Cannot test job completion.');
      return;
    }

    // Wait for job completion
    console.log(`\n⏳ Waiting for ${successful.length} jobs to complete...`);
    const jobPromises = successful.map(upload => 
      waitForJobCompletion(upload.jobId).then(result => ({
        ...upload,
        jobResult: result
      }))
    );

    const jobResults = await Promise.all(jobPromises);

    // Analyze job results
    const completedJobs = jobResults.filter(j => j.jobResult.success);
    const failedJobs = jobResults.filter(j => !j.jobResult.success);

    console.log(`\n📊 Job Completion Results:`);
    console.log(`✅ Completed: ${completedJobs.length}/${successful.length}`);
    console.log(`❌ Failed: ${failedJobs.length}/${successful.length}`);

    if (failedJobs.length > 0) {
      console.log('\n❌ Failed jobs:');
      failedJobs.forEach(j => {
        console.log(`   Job ${j.jobId} (Upload ${j.index}): ${j.jobResult.error}`);
      });
    }

    // Check for duplicate requirement IDs
    if (completedJobs.length > 0) {
      console.log('\n🔍 Checking for duplicate requirement IDs...');
      const requirementIds = completedJobs
        .map(j => j.jobResult.result?.requirement_id)
        .filter(id => id);

      const uniqueIds = new Set(requirementIds);
      
      if (uniqueIds.size === requirementIds.length) {
        console.log(`✅ All ${requirementIds.length} requirement IDs are unique`);
        console.log(`   Sample IDs: ${Array.from(uniqueIds).slice(0, 3).join(', ')}...`);
      } else {
        console.log(`❌ Found duplicate requirement IDs!`);
        console.log(`   Total IDs: ${requirementIds.length}, Unique: ${uniqueIds.size}`);
        
        // Find duplicates
        const idCounts = {};
        requirementIds.forEach(id => {
          idCounts[id] = (idCounts[id] || 0) + 1;
        });
        
        const duplicates = Object.entries(idCounts).filter(([id, count]) => count > 1);
        console.log(`   Duplicates: ${duplicates.map(([id, count]) => `${id} (${count}x)`).join(', ')}`);
      }
    }

    // Final assessment
    console.log('\n🎯 Test Results:');
    const allUploadsSuccessful = successful.length === concurrentUploads;
    const allJobsCompleted = completedJobs.length === successful.length;
    const noRaceCondition = failedJobs.every(j => !j.jobResult.error?.includes('duplicate key'));
    const noDuplicateIds = completedJobs.length === 0 || 
      new Set(completedJobs.map(j => j.jobResult.result?.requirement_id).filter(id => id)).size === 
      completedJobs.filter(j => j.jobResult.result?.requirement_id).length;

    if (allUploadsSuccessful && allJobsCompleted && noRaceCondition && noDuplicateIds) {
      console.log('✅ ALL TESTS PASSED! Race condition has been successfully fixed.');
      console.log('   - All uploads completed successfully');
      console.log('   - All jobs processed without errors');
      console.log('   - No duplicate key violations');
      console.log('   - All requirement IDs are unique');
    } else {
      console.log('❌ SOME TESTS FAILED! Issues detected:');
      if (!allUploadsSuccessful) console.log('   - Some uploads failed');
      if (!allJobsCompleted) console.log('   - Some jobs failed to complete');
      if (!noRaceCondition) console.log('   - Race condition still exists (duplicate key errors)');
      if (!noDuplicateIds) console.log('   - Duplicate requirement IDs found');
    }

  } catch (error) {
    console.error('\n❌ Test failed with error:', error.message);
  }
}

// Run the test
if (require.main === module) {
  runConcurrentTest().catch(console.error);
}

module.exports = { runConcurrentTest };
