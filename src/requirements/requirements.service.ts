import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Requirement, User, RequirementStatus, AiAnalysis, ProcessingMethod, EmbeddingStatus } from '../entities';
import { UploadRequirementDto, QueryRequirementsDto, UploadJobResponseDto, BulkDeleteRequirementsDto, BulkDeleteJobResponseDto } from './dto';
import { GoogleCloudStorageService } from '../storage/google-cloud-storage.service';
import { UploadQueueService } from './services/upload-queue.service';
import { BulkDeleteQueueService } from './services/bulk-delete-queue.service';
import { HybridFileProcessorService } from './services/hybrid-file-processor.service';
import { EmbeddingQueueService } from './services/embedding-queue.service';

@Injectable()
export class RequirementsService {
  private readonly logger = new Logger(RequirementsService.name);
  constructor(
    @InjectRepository(Requirement)
    private requirementRepository: Repository<Requirement>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(RequirementStatus)
    private requirementStatusRepository: Repository<RequirementStatus>,
    @InjectRepository(AiAnalysis)
    private aiAnalysisRepository: Repository<AiAnalysis>,
    private googleCloudStorageService: GoogleCloudStorageService,
    private uploadQueueService: UploadQueueService,
    private bulkDeleteQueueService: BulkDeleteQueueService,
    private hybridFileProcessorService: HybridFileProcessorService,
    private embeddingQueueService: EmbeddingQueueService,
  ) {}

  // New async upload method
  async uploadRequirementAsync(
    uploadDto: UploadRequirementDto,
    fileContent: string,
    fileBuffer: Buffer,
    originalFilename: string,
    mimetype: string,
  ): Promise<UploadJobResponseDto> {
    // Add job to queue and return immediately
    const jobId = await this.uploadQueueService.addUploadJob(
      uploadDto,
      fileBuffer,
      originalFilename,
      mimetype,
      fileContent
    );

    return {
      jobId,
      status: 'queued',
      message: 'Upload job created successfully. Use jobId to track progress.',
      estimatedProcessingTime: 30 // seconds
    };
  }

  // Original synchronous method (kept for backward compatibility)
  async uploadRequirement(
    uploadDto: UploadRequirementDto,
    fileContent: string,
    fileBuffer: Buffer,
    originalFilename: string,
    mimetype: string,
  ): Promise<{ id: string; requirement_id: string; status: string; message: string }> {
    // Find or create user - must match all three fields to ensure proper project association
    let user = await this.userRepository.findOne({
      where: {
        userId: uploadDto.user_id,
        companyId: uploadDto.company_id,
        projectId: uploadDto.project_id
      }
    });

    if (!user) {
      user = this.userRepository.create({
        userId: uploadDto.user_id,
        companyId: uploadDto.company_id,
        projectId: uploadDto.project_id,
      });
      await this.userRepository.save(user);
    }

    // Get draft status ID
    const draftStatus = await this.requirementStatusRepository.findOne({
      where: { name: 'draft' }
    });

    if (!draftStatus) {
      throw new Error('Draft status not found. Please ensure requirement statuses are seeded.');
    }

    // Generate sequential requirement ID per project with race condition protection
    let requirementId: string;
    let attempts = 0;
    const maxAttempts = 10;

    do {
      attempts++;

      // Count existing requirements for this specific project
      const projectRequirementsCount = await this.requirementRepository
        .createQueryBuilder('req')
        .innerJoin('req.uploadedBy', 'user')
        .where('user.projectId = :projectId', { projectId: uploadDto.project_id })
        .getCount();

      const nextNumber = projectRequirementsCount + 1;
      requirementId = `R-${String(nextNumber).padStart(3, '0')}`;

      // Check if this ID already exists for this project (double-check for race conditions)
      const existing = await this.requirementRepository
        .createQueryBuilder('req')
        .innerJoin('req.uploadedBy', 'user')
        .where('req.requirementId = :requirementId', { requirementId })
        .andWhere('user.projectId = :projectId', { projectId: uploadDto.project_id })
        .getOne();

      if (!existing) {
        break; // Found unique ID for this project
      }

      if (attempts >= maxAttempts) {
        throw new Error(`Failed to generate unique requirement ID for project ${uploadDto.project_id} after ${maxAttempts} attempts`);
      }

      // Wait a bit before retrying to reduce contention
      await new Promise(resolve => setTimeout(resolve, 50 + Math.random() * 50));
    } while (attempts < maxAttempts);

    // Upload file to Google Cloud Storage
    const storageUrl = await this.googleCloudStorageService.uploadRequirementFile(
      fileBuffer,
      originalFilename,
      mimetype,
      requirementId,
    );

    // Create requirement with basic content first
    const requirement = this.requirementRepository.create({
      requirementId: requirementId,
      name: uploadDto.name,
      content: fileContent, // This will be the basic extracted content
      statusId: draftStatus.id,
      storageUrl,
      uploadedById: user.id,
      metadata: {
        processingMethod: ProcessingMethod.TRADITIONAL_PARSE,
        embeddingStatus: EmbeddingStatus.NOT_STARTED,
        fileInfo: {
          originalMimeType: mimetype,
          fileSize: fileBuffer.length,
          originalFilename: originalFilename,
        },
        processingTimestamp: new Date(),
      }
    });

    const savedRequirement: Requirement = await this.requirementRepository.save(requirement);

    // Process file using hybrid approach (this will update the requirement if needed)
    try {
      const hybridResult = await this.hybridFileProcessorService.processFile(
        fileBuffer,
        originalFilename,
        mimetype,
        savedRequirement.id
      );

      // Update requirement with hybrid processing results
      await this.requirementRepository.update(savedRequirement.id, {
        content: hybridResult.content,
        metadata: hybridResult.metadata
      });

    } catch (error) {
      this.logger.error(`Hybrid processing failed for requirement ${savedRequirement.id}:`, error);
      // Continue with basic content - the requirement is still valid
    }

    return {
      id: savedRequirement.id,
      requirement_id: savedRequirement.requirementId,
      status: 'draft',
      message: 'File uploaded successfully and is being processed asynchronously'
    };
  }

  async findAll(queryDto: QueryRequirementsDto): Promise<{
    data: any[];
    pagination: { page: number; limit: number; total: number; totalPages: number };
  }> {
    try {
      const {
        status,
        project_id,
        sort_by = 'created_at',
        order = 'DESC',
        page = 1,
        limit = 10
      } = queryDto;

    // Use find with relations instead of query builder to avoid join issues
    let relations = ['status', 'uploadedBy'];

    // Map sort field names to entity property names
    const sortFieldMap: { [key: string]: string } = {
      'created_at': 'createdAt',
      'updated_at': 'updatedAt',
      'status': 'statusId'
    };

    const actualSortField = sortFieldMap[sort_by] || sort_by;

    let requirements = await this.requirementRepository.find({
      relations,
      order: {
        [actualSortField]: order as 'ASC' | 'DESC'
      }
    });

    // Apply filters after loading (since join conditions were problematic)
    if (status) {
      requirements = requirements.filter(req => req.status?.name === status);
    }

    if (project_id) {
      requirements = requirements.filter(req => req.uploadedBy?.projectId === project_id);
    }

    const total = requirements.length;

    // Apply pagination
    const offset = (page - 1) * limit;
    requirements = requirements.slice(offset, offset + limit);

    // Get AI quality scores for all requirements
    const requirementIds = requirements.map(req => req.id);
    const aiAnalyses = await this.getLatestAiAnalyses(requirementIds);

    const data = requirements.map(req => ({
      id: req.id,
      requirement_id: req.requirementId,
      name: req.name,
      status: req.status?.name,
      storage_url: req.storageUrl,
      uploaded_by: req.uploadedBy?.userId,
      created_at: req.createdAt,
      updated_at: req.updatedAt,
      ai_quality_score: aiAnalyses.get(req.id)?.aiQualityScore || null,
    }));

      return {
        data,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error('Error in findAll:', error);
      // Return empty result instead of throwing
      return {
        data: [],
        pagination: {
          page: queryDto.page || 1,
          limit: queryDto.limit || 10,
          total: 0,
          totalPages: 0,
        },
      };
    }
  }

  async findOne(id: string): Promise<any> {
    const requirement = await this.requirementRepository.findOne({
      where: { id },
      relations: ['status', 'uploadedBy'],
    });

    if (!requirement) {
      throw new NotFoundException(`Requirement with ID ${id} not found`);
    }

    // Get the latest AI analysis for this requirement
    const aiAnalyses = await this.getLatestAiAnalyses([id]);
    const latestAnalysis = aiAnalyses.get(id);

    // Debug logging to help troubleshoot
    if (latestAnalysis) {
      this.logger.log(`Found AI analysis for requirement ${id}: score=${latestAnalysis.aiQualityScore}, hasFeedback=${!!latestAnalysis.aiFeedback}`);
    } else {
      this.logger.log(`No AI analysis found for requirement ${id}`);
    }

    return {
      id: requirement.id,
      requirement_id: requirement.requirementId,
      name: requirement.name,
      content: requirement.content,
      status: requirement.status?.name,
      storage_url: requirement.storageUrl,
      uploaded_by: requirement.uploadedBy?.userId,
      created_at: requirement.createdAt,
      updated_at: requirement.updatedAt,
      ai_quality_score: latestAnalysis?.aiQualityScore || null,
      ai_analysis: latestAnalysis ? {
        id: latestAnalysis.id,
        aiQualityScore: latestAnalysis.aiQualityScore,
        aiFeedback: latestAnalysis.aiFeedback,
        analysisDate: latestAnalysis.analysisDate.toISOString()
      } : null,
    };
  }

  // Bulk delete requirements asynchronously
  async bulkDeleteRequirementsAsync(
    bulkDeleteDto: BulkDeleteRequirementsDto,
  ): Promise<BulkDeleteJobResponseDto> {
    // Add job to queue and return immediately
    const jobId = await this.bulkDeleteQueueService.addBulkDeleteJob(bulkDeleteDto);

    return {
      jobId,
      status: 'queued',
      message: 'Bulk delete job created successfully. Use jobId to track progress.',
      totalRequirements: bulkDeleteDto.requirementIds.length,
      estimatedProcessingTime: Math.max(30, bulkDeleteDto.requirementIds.length * 5) // 5 seconds per requirement, minimum 30 seconds
    };
  }

  async updateMetadata(id: string, metadata: Record<string, any>): Promise<void> {
    try {
      await this.requirementRepository.update(id, {
        metadata: metadata as any
      });
      this.logger.log(`Updated metadata for requirement ${id}`);
    } catch (error) {
      this.logger.error(`Error updating metadata for requirement ${id}:`, error);
      throw error;
    }
  }

  async validateUserCredentials(
    userId: string,
    companyId: string,
    projectId: string
  ): Promise<boolean> {
    try {
      const user = await this.userRepository.findOne({
        where: {
          userId: userId,
          companyId: companyId,
          projectId: projectId
        }
      });

      const exists = !!user;
      this.logger.log(`User validation result for ${userId}/${companyId}/${projectId}: ${exists ? 'VALID' : 'INVALID'}`);
      return exists;
    } catch (error) {
      this.logger.error(`Error validating user credentials for ${userId}/${companyId}/${projectId}:`, error);
      return false;
    }
  }

  async updateContext(id: string, context: string): Promise<void> {
    try {
      await this.requirementRepository.update(id, { context });
      this.logger.log(`Updated context for requirement ${id}`);
    } catch (error) {
      this.logger.error(`Error updating context for requirement ${id}:`, error);
      throw error;
    }
  }

  async updateContent(id: string, content: string): Promise<void> {
    try {
      await this.requirementRepository.update(id, { content });
      this.logger.log(`Updated content for requirement ${id}`);
    } catch (error) {
      this.logger.error(`Error updating content for requirement ${id}:`, error);
      throw error;
    }
  }

  async updateStatus(id: string, statusName: string): Promise<void> {
    try {
      // Get the status ID from the status name
      const status = await this.requirementStatusRepository.findOne({
        where: { name: statusName }
      });

      if (!status) {
        throw new NotFoundException(`Status '${statusName}' not found`);
      }

      await this.requirementRepository.update(id, { statusId: status.id });
      this.logger.log(`Updated status for requirement ${id} to '${statusName}'`);
    } catch (error) {
      this.logger.error(`Error updating status for requirement ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get the latest AI analysis for each requirement ID
   * Returns a Map with requirement ID as key and latest analysis as value
   */
  private async getLatestAiAnalyses(requirementIds: string[]): Promise<Map<string, AiAnalysis>> {
    if (requirementIds.length === 0) {
      return new Map();
    }

    try {
      // Alternative approach: Get all analyses for the requirements and filter in memory
      // This is more reliable than complex subqueries with TypeORM
      const allAnalyses = await this.aiAnalysisRepository
        .createQueryBuilder('analysis')
        .select([
          'analysis.id',
          'analysis.requirementId',
          'analysis.aiQualityScore',
          'analysis.aiFeedback',
          'analysis.analysisDate'
        ])
        .where('analysis.requirementId IN (:...requirementIds)', { requirementIds })
        .orderBy('analysis.analysisDate', 'DESC')
        .getMany();

      // Group by requirement ID and take the latest (first) analysis for each
      const analysisMap = new Map<string, AiAnalysis>();
      allAnalyses.forEach(analysis => {
        if (!analysisMap.has(analysis.requirementId)) {
          analysisMap.set(analysis.requirementId, analysis);
        }
      });

      return analysisMap;
    } catch (error) {
      this.logger.error('Error fetching latest AI analyses:', error);
      // Return empty map on error to avoid breaking the main query
      return new Map();
    }
  }
}
