#!/usr/bin/env node

/**
 * Test script to verify sequential requirement ID generation per project
 * 
 * This test verifies that:
 * 1. Requirements get sequential IDs per project (R-001, R-002, R-003...)
 * 2. Different projects have independent numbering sequences
 * 3. Concurrent uploads within the same project maintain proper sequence
 */

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

// Configuration
const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';
const JWT_TOKEN = process.env.JWT_TOKEN || 'your-jwt-token-here';

// Test data
const TEST_USER_ID = 'test-user-sequential';
const TEST_COMPANY_ID = 'test-company-sequential';
const PROJECT_A_ID = 'project-a-sequential';
const PROJECT_B_ID = 'project-b-sequential';

async function uploadRequirement(projectId, requirementName, index) {
  try {
    const filename = `sequential-test-${projectId}-${index}.txt`;
    const content = `Sequential Test ${index} for ${projectId}\n\nThis is test file number ${index} for project ${projectId}.\n\nFeatures:\n- Feature ${index}A\n- Feature ${index}B`;
    
    // Create test file
    fs.writeFileSync(filename, content);

    const formData = new FormData();
    formData.append('file', fs.createReadStream(filename));
    formData.append('name', requirementName);
    formData.append('user_id', TEST_USER_ID);
    formData.append('company_id', TEST_COMPANY_ID);
    formData.append('project_id', projectId);

    const response = await axios.post(
      `${BASE_URL}/api/v1/requirements/upload/async`,
      formData,
      {
        headers: {
          'Authorization': `Bearer ${JWT_TOKEN}`,
          ...formData.getHeaders()
        }
      }
    );

    console.log(`✅ Uploaded "${requirementName}" to ${projectId} - Job ID: ${response.data.jobId}`);
    
    // Cleanup test file
    fs.unlinkSync(filename);
    
    return {
      projectId,
      index,
      jobId: response.data.jobId,
      requirementName,
      success: true
    };
  } catch (error) {
    console.error(`❌ Upload failed for ${projectId} #${index}:`, error.response?.data || error.message);
    
    // Cleanup test file if it exists
    const filename = `sequential-test-${projectId}-${index}.txt`;
    try {
      fs.unlinkSync(filename);
    } catch (e) {
      // Ignore cleanup errors
    }
    
    return {
      projectId,
      index,
      error: error.response?.data || error.message,
      success: false
    };
  }
}

async function waitForJobCompletion(jobId, maxWaitTime = 30000) {
  const startTime = Date.now();
  
  while (Date.now() - startTime < maxWaitTime) {
    try {
      const response = await axios.get(
        `${BASE_URL}/api/v1/requirements/jobs/${jobId}/status`,
        {
          headers: { 'Authorization': `Bearer ${JWT_TOKEN}` }
        }
      );

      const status = response.data.status;

      if (status === 'completed') {
        return { success: true, result: response.data.result };
      } else if (status === 'failed') {
        return { success: false, error: response.data.error };
      }

      // Wait 1 second before checking again
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  }

  return { success: false, error: 'Timeout waiting for job completion' };
}

async function getRequirementsByProject(projectId) {
  try {
    const response = await axios.get(
      `${BASE_URL}/api/v1/requirements?project_id=${projectId}&limit=50`,
      {
        headers: { 'Authorization': `Bearer ${JWT_TOKEN}` }
      }
    );

    return response.data.data;
  } catch (error) {
    console.error(`❌ Failed to get requirements for project ${projectId}:`, error.response?.data || error.message);
    return [];
  }
}

async function runSequentialTest() {
  console.log('🧪 Testing Sequential Requirement ID Generation Per Project\n');
  console.log('This test verifies that requirement IDs are sequential per project');
  console.log('(R-001, R-002, R-003...) and independent between projects.\n');

  try {
    // Step 1: Upload requirements to Project A
    console.log('📤 Step 1: Uploading 3 requirements to Project A...');
    const projectAUploads = [];
    for (let i = 1; i <= 3; i++) {
      const result = await uploadRequirement(PROJECT_A_ID, `Project A Requirement ${i}`, i);
      projectAUploads.push(result);
      // Small delay to ensure order
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Step 2: Upload requirements to Project B
    console.log('\n📤 Step 2: Uploading 3 requirements to Project B...');
    const projectBUploads = [];
    for (let i = 1; i <= 3; i++) {
      const result = await uploadRequirement(PROJECT_B_ID, `Project B Requirement ${i}`, i);
      projectBUploads.push(result);
      // Small delay to ensure order
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Step 3: Upload more to Project A to test continuation
    console.log('\n📤 Step 3: Uploading 2 more requirements to Project A...');
    for (let i = 4; i <= 5; i++) {
      const result = await uploadRequirement(PROJECT_A_ID, `Project A Requirement ${i}`, i);
      projectAUploads.push(result);
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Step 4: Wait for all jobs to complete
    console.log('\n⏳ Step 4: Waiting for all jobs to complete...');
    const allUploads = [...projectAUploads, ...projectBUploads];
    const successfulUploads = allUploads.filter(u => u.success);
    
    console.log(`Waiting for ${successfulUploads.length} jobs...`);
    
    const jobResults = await Promise.all(
      successfulUploads.map(async upload => {
        const result = await waitForJobCompletion(upload.jobId);
        return { ...upload, jobResult: result };
      })
    );

    const completedJobs = jobResults.filter(j => j.jobResult.success);
    console.log(`✅ ${completedJobs.length} jobs completed successfully`);

    // Step 5: Verify sequential numbering
    console.log('\n🔍 Step 5: Verifying sequential requirement IDs...');
    
    // Get requirements for both projects
    const projectARequirements = await getRequirementsByProject(PROJECT_A_ID);
    const projectBRequirements = await getRequirementsByProject(PROJECT_B_ID);

    console.log(`\n📊 Results:`);
    console.log(`Project A: ${projectARequirements.length} requirements`);
    console.log(`Project B: ${projectBRequirements.length} requirements`);

    // Extract and sort requirement IDs
    const projectAIds = projectARequirements
      .filter(req => req.name.includes('Project A'))
      .map(req => req.requirement_id)
      .sort();
    
    const projectBIds = projectBRequirements
      .filter(req => req.name.includes('Project B'))
      .map(req => req.requirement_id)
      .sort();

    console.log(`\nProject A IDs: ${projectAIds.join(', ')}`);
    console.log(`Project B IDs: ${projectBIds.join(', ')}`);

    // Verify Project A has sequential IDs
    const expectedProjectAIds = ['R-001', 'R-002', 'R-003', 'R-004', 'R-005'];
    const projectACorrect = projectAIds.length >= 5 && 
      expectedProjectAIds.every((expectedId, index) => projectAIds[index] === expectedId);

    // Verify Project B has sequential IDs starting from 1
    const expectedProjectBIds = ['R-001', 'R-002', 'R-003'];
    const projectBCorrect = projectBIds.length >= 3 && 
      expectedProjectBIds.every((expectedId, index) => projectBIds[index] === expectedId);

    console.log('\n✅ Verification Results:');
    
    if (projectACorrect) {
      console.log('✅ Project A: Sequential numbering correct (R-001 to R-005)');
    } else {
      console.log('❌ Project A: Sequential numbering incorrect');
      console.log(`   Expected: ${expectedProjectAIds.join(', ')}`);
      console.log(`   Actual: ${projectAIds.join(', ')}`);
    }

    if (projectBCorrect) {
      console.log('✅ Project B: Sequential numbering correct (R-001 to R-003)');
    } else {
      console.log('❌ Project B: Sequential numbering incorrect');
      console.log(`   Expected: ${expectedProjectBIds.join(', ')}`);
      console.log(`   Actual: ${projectBIds.join(', ')}`);
    }

    // Verify independence
    const hasIndependentNumbering = projectAIds.includes('R-001') && projectBIds.includes('R-001');
    if (hasIndependentNumbering) {
      console.log('✅ Independent numbering: Both projects start from R-001 (correct)');
    } else {
      console.log('❌ Independent numbering: Projects do not have independent sequences');
    }

    // Final result
    console.log('\n🎯 Test Results:');
    if (projectACorrect && projectBCorrect && hasIndependentNumbering) {
      console.log('✅ ALL TESTS PASSED! Sequential requirement ID generation is working correctly.');
      console.log('   - Project A has sequential IDs: R-001, R-002, R-003, R-004, R-005');
      console.log('   - Project B has sequential IDs: R-001, R-002, R-003');
      console.log('   - Each project has independent numbering sequences');
    } else {
      console.log('❌ TESTS FAILED! Sequential requirement ID generation needs fixing.');
    }

  } catch (error) {
    console.error('\n❌ Test failed with error:', error.message);
  }
}

// Run the test
if (require.main === module) {
  runSequentialTest().catch(console.error);
}

module.exports = { runSequentialTest };
